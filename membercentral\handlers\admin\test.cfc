component extends="BaseHandler" {

	function nestedConfirmationTest(event, rc, prc) {
		// Set up the test page
		prc.pageTitle = "Nested Confirmation Test";
		
		// Render the test page in a modal
		event.setView("admin/test/nestedConfirmationTest");
	}
	
	function showTestModal(event, rc, prc) {
		// This handler can be used to show the test modal from anywhere
		// Usage: /?pg=admin&mca_ajaxfunc=showTestModal
		
		var testContent = renderView("admin/test/dsp_nestedConfirmationTest");
		
		return {
			"success": true,
			"html": testContent
		};
	}

}
