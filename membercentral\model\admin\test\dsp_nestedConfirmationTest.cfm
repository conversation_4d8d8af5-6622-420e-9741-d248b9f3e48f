<cfsavecontent variable="local.testJS">
<cfoutput>
<script type="text/javascript">
// Test functions for the nested confirmation system

function testBasicConfirmation() {
    top.MCModalUtils.showNestedConfirmation({
        title: 'Basic Test',
        message: 'This is a basic confirmation test. Click Confirm to see success message.',
        onConfirm: function() {
            alert('Basic confirmation successful!');
        },
        onCancel: function() {
            alert('Basic confirmation cancelled.');
        }
    });
}

function testDeleteConfirmation() {
    top.MCModalUtils.confirmDelete('Test Item', function() {
        alert('Delete confirmation successful! Item would be deleted.');
    }, function() {
        alert('Delete cancelled.');
    });
}

function testSaveConfirmation() {
    top.MCModalUtils.confirmSave(
        function() { 
            alert('Save & Continue selected!'); 
        },
        function() { 
            alert('Discard Changes selected!'); 
        },
        function() { 
            alert('Save operation cancelled.'); 
        }
    );
}

function testNavigationConfirmation() {
    top.MCModalUtils.confirmNavigation('/test-destination', function() {
        alert('Navigation confirmed! Would navigate to /test-destination');
    }, function() {
        alert('Navigation cancelled.');
    });
}

function testCustomConfirmation() {
    top.MCModalUtils.confirmAction(
        'Custom Test',
        'This is a custom confirmation with warning styling.',
        'Proceed',
        function() {
            alert('Custom confirmation successful!');
        },
        {
            confirmButtonClass: 'btn-warning',
            size: 'lg'
        }
    );
}

function testIframeRequest() {
    top.MCModalUtils.requestParentConfirmation({
        title: 'iframe Test',
        message: 'This confirmation was requested from within an iframe.',
        confirmLabel: 'Success!',
        onConfirm: function() {
            alert('iframe confirmation successful!');
        }
    });
}

function testErrorHandling() {
    top.MCModalUtils.showNestedConfirmation({
        title: 'Error Test',
        message: 'This test will trigger an error in the callback.',
        onConfirm: function() {
            // Intentional error for testing
            nonExistentFunction();
        }
    });
}

function testNoRestore() {
    top.MCModalUtils.showNestedConfirmation({
        title: 'No Restore Test',
        message: 'This confirmation will not restore the parent modal.',
        restoreParent: false,
        onConfirm: function() {
            alert('Confirmation successful! Parent modal will not be restored.');
        }
    });
}

// Test runner function
function runAllTests() {
    const tests = [
        { name: 'Basic Confirmation', func: testBasicConfirmation },
        { name: 'Delete Confirmation', func: testDeleteConfirmation },
        { name: 'Save Confirmation', func: testSaveConfirmation },
        { name: 'Navigation Confirmation', func: testNavigationConfirmation },
        { name: 'Custom Confirmation', func: testCustomConfirmation },
        { name: 'iframe Request', func: testIframeRequest },
        { name: 'Error Handling', func: testErrorHandling },
        { name: 'No Restore', func: testNoRestore }
    ];
    
    let currentTest = 0;
    
    function runNextTest() {
        if (currentTest < tests.length) {
            const test = tests[currentTest];
            console.log('Running test:', test.name);
            test.func();
            currentTest++;
            
            // Wait for user to complete test before running next
            setTimeout(function() {
                if (confirm('Test "' + test.name + '" completed. Run next test?')) {
                    runNextTest();
                }
            }, 2000);
        } else {
            alert('All tests completed!');
        }
    }
    
    runNextTest();
}

$(function() {
    // Add footer with test buttons
    top.MCModalUtils.buildFooter({
        classlist: 'd-flex flex-wrap',
        showclose: true,
        buttons: [
            { class: "btn-primary btn-sm m-1", clickhandler: 'testBasicConfirmation', label: 'Basic Test', id: 'btnBasicTest' },
            { class: "btn-danger btn-sm m-1", clickhandler: 'testDeleteConfirmation', label: 'Delete Test', id: 'btnDeleteTest' },
            { class: "btn-warning btn-sm m-1", clickhandler: 'testSaveConfirmation', label: 'Save Test', id: 'btnSaveTest' },
            { class: "btn-info btn-sm m-1", clickhandler: 'testNavigationConfirmation', label: 'Navigation Test', id: 'btnNavTest' },
            { class: "btn-secondary btn-sm m-1", clickhandler: 'testCustomConfirmation', label: 'Custom Test', id: 'btnCustomTest' },
            { class: "btn-success btn-sm m-1", clickhandler: 'testIframeRequest', label: 'iframe Test', id: 'btnIframeTest' },
            { class: "btn-dark btn-sm m-1", clickhandler: 'testErrorHandling', label: 'Error Test', id: 'btnErrorTest' },
            { class: "btn-outline-primary btn-sm m-1", clickhandler: 'testNoRestore', label: 'No Restore Test', id: 'btnNoRestoreTest' }
        ]
    });
});
</script>
</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.testJS)#">

<cfoutput>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h3>MCModalUtils Nested Confirmation System Test</h3>
            <p class="text-muted">This page tests the nested confirmation system functionality.</p>
            
            <div class="alert alert-info">
                <h5><i class="fa-solid fa-info-circle"></i> Test Instructions</h5>
                <ul class="mb-0">
                    <li>Click any test button in the footer to run individual tests</li>
                    <li>Each test will show a confirmation dialog</li>
                    <li>Try both confirming and cancelling to test both paths</li>
                    <li>Check browser console for any errors</li>
                    <li>Verify that the parent modal is properly restored after each test</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5>Test Results</h5>
                </div>
                <div class="card-body">
                    <div id="testResults">
                        <p>Click test buttons in the footer to begin testing.</p>
                        <p><strong>Expected Behavior:</strong></p>
                        <ul>
                            <li>Confirmation dialog should appear over the current modal</li>
                            <li>Parent modal should be hidden during confirmation</li>
                            <li>After confirmation/cancellation, parent modal should be restored</li>
                            <li>Callbacks should execute properly</li>
                            <li>No JavaScript errors should occur</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5>Test Scenarios</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Basic Tests</h6>
                            <ul>
                                <li><strong>Basic Test:</strong> Simple confirmation with custom message</li>
                                <li><strong>Delete Test:</strong> Delete confirmation with danger styling</li>
                                <li><strong>Save Test:</strong> Three-button save/discard/cancel dialog</li>
                                <li><strong>Navigation Test:</strong> Navigation confirmation with warning</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Advanced Tests</h6>
                            <ul>
                                <li><strong>Custom Test:</strong> Custom styling and size options</li>
                                <li><strong>iframe Test:</strong> Confirmation requested from iframe context</li>
                                <li><strong>Error Test:</strong> Error handling in callback functions</li>
                                <li><strong>No Restore Test:</strong> Confirmation without parent restoration</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5>Debug Information</h5>
                </div>
                <div class="card-body">
                    <p>To enable debug logging, run in browser console:</p>
                    <code>window.MCModalDebug = true;</code>
                    
                    <p class="mt-2">Current modal state:</p>
                    <ul>
                        <li>Modal shown: <span id="modalState">Loading...</span></li>
                        <li>Parent window: <span id="parentState">Loading...</span></li>
                        <li>iframe context: <span id="iframeState">Loading...</span></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Update debug information
$(function() {
    $('#modalState').text(top.MCModalUtils.isShown() ? 'Yes' : 'No');
    $('#parentState').text(window.parent === window ? 'Parent' : 'Child');
    $('#iframeState').text(window !== top ? 'Yes' : 'No');
});
</script>
</cfoutput>
