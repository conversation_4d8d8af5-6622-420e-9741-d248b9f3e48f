<cfsavecontent variable="local.itemsJS">
	<cfoutput>
	<script language="JavaScript">
		function initSWBListItemsTable(){
		SWBListItemsTable = $('##SWBListItemsTable').DataTable({
			"processing": true,
			"serverSide": true,
			"paginate": false,
			"language": {
				"lengthMenu": "_MENU_"
			},
			"ajax": { 
				"url": link_listincswbitems,
				"type": "post",
				"data": function(d) { 
								if (window.reorderData && window.reorderData.length > 0) { 
									d.reorderData = JSON.stringify(window.reorderData); 
									window.reorderData = [];
								} 
								return d; 
						}
			},
			"autoWidth": false,
			"columns": [ 
				{
					"data": null,
					"render": function (data, type) {
						let renderData = '';
						if (type === 'display') {
							renderData += '<i class="fa-light fa-bars"></i>';
							
						}
						return type === 'display' ? renderData : data;
					},
					"width": "5%",
					"orderable": false
				},
				<cfif NOT local.qryBundle.isSWOD>
				{
					"data": null,
					"render": function (data, type) {
						let renderData = '';
						if (type === 'display') {
							renderData = data.formattedDateStart + '<div class="pt-1 pl-2 small text-dim">' + data.formattedTimeStart + ' ' + timeZoneAbbr +'</div>';
						}
						return type === 'display' ? renderData : data;
					},
					"width": "15%",
					"className": "align-top"
				},
				</cfif>
				{ "data": null,
					"render": function ( data, type, row, meta ) {
						return type === 'display' ? data.contentName + (!data.contentPublished ? '<span class="badge badge-warning ml-2">Inactive</span>' : '') + (data.lockSettings ? '<span class="fa-solid fa-lock ml-1" style="font-size: 17px; color: grey;" title="Changes to this program are restricted by a Client Administrator."></span>' : '') + (data.contentSubTitle.length > 0 ? '<div class="small text-dim">'+data.contentSubTitle+'</div>' : '') : data;
					}
				}
				<cfif local.qryBundle.isSWOD>
				,{ "data": null,
					"render": function (data, type) {
							let renderData = '';
							if (type === 'display') {
								renderData += data.creditExpired + '<br/><div class="text-dim small">'+data.creditDetails+'</div>'
							}
							return type === 'display' ? renderData : data;
						},
						"width": "15%", 
						"className": "align-top text-center"
				},
				{ "data": null,
					"render": function (data, type) {
							let renderData = '';
							if (type === 'display') {
								renderData += data.catalogSaleExpired + '<br/> <div class="text-dim small">'+data.catalogSaleDetails+'</div>'
							}
							return type === 'display' ? renderData : data;
						},
						"width": "15%", 
						"className": "align-top text-center"
				}
				</cfif>
				<cfif local.isPublisher>
				,{ "data": null,
					"render": function ( data, type, row, meta ) {
						let renderData = '';
						if (type === 'display') {
							renderData += '<a href="##" class="btn btn-xs btn-outline-primary p-1 m-1 <cfif NOT local.isPublisher>readOnly</cfif>" title="Edit Program" onclick="javascript:edit'+ data.contentType + 'Program('+data.contentID+');closeProgramTabSection(\'program-seminars\');return false;"><i class="fa-solid fa-pencil"></i></a>';
							if (!data.isBundleProgramLocked) {
								renderData += '<a href="##" class="btn btn-xs btn-outline-primary p-1 m-1 <cfif NOT local.isPublisher>readOnly</cfif>" onclick="overrideSWBItemDescription('+data.itemID+');return false;" title="Override Program Description"><i class="fa-solid fa-pen-to-square"></i></a>';
								renderData += '<a href="##" id="btnDelSWBSeminar'+data.contentID+'" class="btn btn-xs btn-outline-danger p-1 m-1 <cfif NOT local.isPublisher>readOnly</cfif>" onclick="removeSeminarFromBundle('+data.contentID+');return false;" title="Remove"><i class="fa-solid fa-trash-can"></i></a>';
							}
						}
						return type === 'display' ? renderData : data;
					},
					"width": "15%",
					"className": "text-center"
				}
				</cfif>
			],
			"ordering": false,
			"searching": false,
			"rowReorder": {
					dataSrc: "columnid" 
				}
			<cfif local.programAdded>
				,"drawCallback": function() {
					if ($('##SWBListItemsTable tbody').find('.badge-warning').length > 0) {
						hasInactiveSeminarsIncluded = true;
					}
					else hasInactiveSeminarsIncluded = false;
					hasIncludedSeminarsCount = $('##SWBListItemsTable tbody tr').length;
					if($('##SWBListItemsTable tbody tr').length == 2)
						mca_hideAlert('err_addprogram');
				}
			<cfelse>
				,"drawCallback": function() {
					if ($('##SWBListItemsTable tbody').find('.badge-warning').length > 0) {
						hasInactiveSeminarsIncluded = true;
					}
					else hasInactiveSeminarsIncluded = false;
					hasIncludedSeminarsCount = $('##SWBListItemsTable tbody tr').length;
					if ((hasInactiveSeminarsIncluded) && $('##bundleStatusHidden').val() === 'A' && !programAdded) {
						// Determine the message based on the condition
						let message = "You cannot have an Active bundle with included programs marked as 'Inactive'. Click OK to mark the bundle as Inactive & to continue editing your Included Programs.";
							
						// Show confirm pop-up
						MCModalUtils.showModal({
							verticallycentered: true,
							size: 'md',
							title: 'Confirmation Needed',
							strmodalbody: {
								content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>' + message + '</span></div>' 
							},
							strmodalfooter: {
								classlist: 'd-flex',
								showclose: true,
								showextrabutton: true,
								extrabuttonclass: 'btn-primary ml-auto',
								extrabuttonlabel: 'OK'
							}
						});
						$('##btnMCModalSave').on('click', function(){
							MCModalUtils.hideModal();
							$('##bundleStatus').val('I'); // Update UI display
							validateAndSaveSWBProgram(); // Save the updated status
						});
						$('##MCModal').on('hidden.bs.modal', function() {
							$('##btnMCModalSave').off('click');
						});
					}
					if(!hasInactiveSeminarsIncluded && hasIncludedSeminarsCount > 1)
						mca_hideAlert('err_swbdetails');
				}
			</cfif>
		});
		SWBListItemsTable.on('row-reorder', function (e, diff, edit) {
			let orderData = [];
			diff.forEach(function(item){
				orderData.push({
					id: SWBListItemsTable.row(item.node).data().itemID,
					newOrder: item.newPosition
				});
			});
			window.reorderData = orderData;
		});
	}
	function validateIfSeminarsAddedToBundle(callback){
		mca_hideAlert('err_addprogram');
		var arrReq = [];
		if (programAdded && $('##SWBListItemsTable tbody tr').length < 2) {
			arrReq.push('Add at least 2 seminars to the bundle to proceed to the next step.');
		}
		if(arrReq.length){
			$('##err_addprogram').html(arrReq.join('<br/>')).removeClass('d-none');
			$('html,body').animate({scrollTop: $('##err_addprogram').offset().top-120},500);
			if(programAdded)
				$('##nextButton').prop('disabled',false);
			else
				$('##program-seminars .card-footer .save-button').prop('disabled',false);
		}
		if (callback) {
			callback();
		}
	}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.itemsJS#">

<cfoutput>
<div id="err_addprogram" class="alert alert-danger mb-2 d-none"></div>

<cfif local.isPublisher>
	<cfif local.qryBundle.isSWOD is 1>
		<div id="divAddSWODProgramForm" class="addSWProgramForm">
			<div id="divAddSWODProgramFormArea" class="my-3">
				<form name="frmAddSWODProgram" id="frmAddSWODProgram">
				<div id="err_addswodprogram" class="alert alert-danger mb-2 d-none"></div>
				<div class="form-group row">
					<div class="col-sm">
						<select name="fSWODProgram" id="fSWODProgram" class="form-control form-control-sm"></select>
					</div>
					<div class="col-sm-auto pl-sm-0">
						<button type="button" id="btnAddSWODProgram" name="btnAddSWODProgram" class="btn btn-sm btn-primary" onclick="doAddSWODProgramToBundle();">Add OnDemand Seminar</button>
					</div>
				</div>
				</form>
			</div>
		</div>
	<cfelse>
		<div id="divAddSWLProgramForm" class="addSWProgramForm">
			<div id="divAddSWLProgramFormArea" class="my-3">
				<form name="frmAddSWL" id="frmAddSWL">
				<div id="err_addswlprogram" class="alert alert-danger mb-2 d-none"></div>
				<div class="form-group row">
					<div class="col-sm">
						<select name="fSWLProgram" id="fSWLProgram" class="form-control form-control-sm"></select>
					</div>
					<div class="col-sm-auto pl-sm-0">
						<button type="button" id="btnAddSWLProgram" name="btnAddSWLProgram" class="btn btn-sm btn-primary" onclick="doAddSWLProgramToBundle();">Add Live Webinar</button>
					</div>
				</div>
				</form>
			</div>
		</div>
	</cfif>
</cfif>
<div id="divProgramDescriptionForm" class="addSWProgramForm d-none my-3">
	<form name="frmSWBItemDescription" id="frmSWBItemDescription">
	<input type="hidden" name="swbItemID" id="swbItemID" value="0">
	<h5>Override Program Description</h5>
	<div class="form-group row mt-3">
		<label for="swbItemDescription" class="col-sm-3 col-form-label-sm font-size-md">Description</label>
		<div class="col-sm-9">
			<textarea name="swbItemDescription" id="swbItemDescription" class="form-control form-control-sm" rows="6"></textarea>
		</div>
	</div>
	<div class="form-group row mt-2">
		<div class="offset-sm-3 col-sm-9">
			<button type="button" id="btnSaveItemDescription" class="btn btn-sm btn-primary" onclick="saveSWBItemDescription();">Save</button>
		</div>
	</div>
	</form>
</div>
<table id="SWBListItemsTable" class="table table-sm table-striped table-bordered" style="width:100%">
	<thead>
		<tr>
			<th id="columnid"></th>
			<cfif NOT local.qryBundle.isSWOD>
				<th>Program Date</th>
			</cfif>
			<th>Program Name</th>
			<cfif local.qryBundle.isSWOD>
				<th>Credit Expired</th>
				<th>Catalog Sale Expired</th>
			</cfif>
			<cfif local.isPublisher>
				<th>Tools</th>
			</cfif>
		</tr>
	</thead>
</table>
</cfoutput>