# MCModalUtils Nested Confirmation System

## Overview

The MCModalUtils Nested Confirmation System provides a standardized way to display confirmation dialogs from within existing iframe-based modals. This system is based on the proven `downloadSWTLFile` pattern and ensures proper modal sequencing, backdrop management, and parent modal restoration.

## Core Function: showNestedConfirmation()

### Basic Usage

```javascript
MCModalUtils.showNestedConfirmation({
    title: 'Delete Item',
    message: 'Are you sure you want to delete this item?',
    confirmLabel: 'Delete',
    cancelLabel: 'Cancel',
    onConfirm: function() {
        // Perform delete action
        deleteItem();
    },
    onCancel: function() {
        // Optional cancel action
        console.log('Delete cancelled');
    }
});
```

### Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `title` | String | 'Confirmation Required' | Modal title |
| `message` | String | 'Are you sure you want to continue?' | Confirmation message |
| `confirmLabel` | String | 'Confirm' | Confirm button text |
| `cancelLabel` | String | 'Cancel' | Cancel button text |
| `onConfirm` | Function | null | Callback when confirmed |
| `onCancel` | Function | null | Callback when cancelled |
| `restoreParent` | Boolean | true | Whether to restore parent modal |
| `confirmButtonClass` | String | 'btn-primary' | CSS classes for confirm button |
| `size` | String | 'md' | Modal size ('sm', 'md', 'lg', 'xl') |

## Helper Functions

### confirmDelete()

Specialized confirmation for delete operations with danger styling.

```javascript
MCModalUtils.confirmDelete('User Account', function() {
    // Delete the user account
    deleteUserAccount();
});
```

### confirmSave()

Three-button confirmation for unsaved changes scenarios.

```javascript
MCModalUtils.confirmSave(
    function() { saveChanges(); navigateAway(); }, // Save & Continue
    function() { discardChanges(); navigateAway(); }, // Discard Changes
    function() { console.log('Cancelled'); } // Cancel
);
```

### confirmNavigation()

Confirmation for navigation that might lose data.

```javascript
MCModalUtils.confirmNavigation('/new-page', function() {
    window.location.href = '/new-page';
});
```

### confirmAction()

Generic confirmation with custom parameters.

```javascript
MCModalUtils.confirmAction(
    'Custom Title',
    'Custom message here',
    'Custom Button',
    function() { performCustomAction(); },
    { confirmButtonClass: 'btn-warning', size: 'lg' }
);
```

## iframe Integration

### From Within an iframe

Use `requestParentConfirmation()` to trigger confirmations from iframe content:

```javascript
// In iframe content
top.MCModalUtils.requestParentConfirmation({
    message: 'Save changes before closing?',
    onConfirm: function() {
        // This runs in parent context
        saveFormData();
        closeModal();
    }
});
```

### ColdFusion Integration Example

```html
<script>
function confirmDeleteRecord(recordId) {
    top.MCModalUtils.confirmDelete('Record #' + recordId, function() {
        // Submit delete form
        $('#deleteForm input[name="recordId"]').val(recordId);
        $('#deleteForm').submit();
    });
}
</script>
```

## Advanced Usage Examples

### Complex Multi-Step Confirmation

```javascript
function complexWorkflow() {
    MCModalUtils.showNestedConfirmation({
        title: 'Multi-Step Process',
        message: 'This will perform multiple operations. Continue?',
        size: 'lg',
        onConfirm: function() {
            // Show progress and perform operations
            performStep1()
                .then(() => performStep2())
                .then(() => performStep3())
                .catch(handleError);
        }
    });
}
```

### Conditional Restoration

```javascript
MCModalUtils.showNestedConfirmation({
    message: 'Close without saving?',
    restoreParent: false, // Don't restore parent
    onConfirm: function() {
        // Close everything
        window.location.href = '/dashboard';
    },
    onCancel: function() {
        // Manually restore parent if needed
        MCModalUtils._restoreParentModal();
    }
});
```

## Technical Implementation Details

### Event Flow

1. **Parent State Capture**: Current modal title, content, and footer are stored
2. **Parent Modal Hide**: `MCModalUtils.hideModal()` is called
3. **Event Listener**: One-time `hidden.bs.modal` event listener waits for parent to hide
4. **Confirmation Display**: New confirmation modal is shown with proper configuration
5. **User Action**: User clicks confirm, cancel, or closes modal
6. **Callback Execution**: Appropriate callback function is executed
7. **Parent Restoration**: Original modal content is restored and shown

### Error Handling

- Validates required parameters before execution
- Catches and logs callback execution errors
- Provides fallback behavior for missing parent modals
- Cleans up global state to prevent memory leaks

### Browser Compatibility

- Works with Bootstrap 4.x and 5.x
- Compatible with IE11+ and all modern browsers
- Uses standard JavaScript features for maximum compatibility

## Best Practices

### Do's
- ✅ Always provide meaningful confirmation messages
- ✅ Use appropriate helper functions for common scenarios
- ✅ Test callback functions thoroughly
- ✅ Handle both confirm and cancel scenarios
- ✅ Use semantic button classes (btn-danger for delete, etc.)

### Don'ts
- ❌ Don't nest confirmations more than one level deep
- ❌ Don't rely on global variables in callbacks
- ❌ Don't forget to handle error cases in callbacks
- ❌ Don't use for simple yes/no questions (use native confirm() instead)
- ❌ Don't modify the parent modal state during confirmation

## Migration from Native confirm()

### Before (Native confirm)
```javascript
if (confirm('Delete this item?')) {
    deleteItem();
}
```

### After (MCModalUtils)
```javascript
MCModalUtils.confirmDelete('item', function() {
    deleteItem();
});
```

## Troubleshooting

### Common Issues

1. **Parent modal not found**: Ensure the confirmation is called from within an active modal
2. **Callbacks not executing**: Check for JavaScript errors in callback functions
3. **Modal not restoring**: Verify `restoreParent` is set to true (default)
4. **Multiple confirmations**: Avoid calling showNestedConfirmation while another is active

### Debug Mode

Enable debug logging by setting:
```javascript
window.MCModalDebug = true;
```

This will log state changes and event handling to the browser console.
