<cfoutput>
<div class="container-fluid">
	<div class="row">
		<div class="col-12">
			<h2>MCModalUtils Nested Confirmation Test</h2>
			<p>Click the button below to open a test modal and try the nested confirmation system.</p>
			
			<button type="button" class="btn btn-primary" onclick="openTestModal()">
				<i class="fa-solid fa-test-tube"></i> Open Test Modal
			</button>
			
			<div class="mt-4">
				<h4>How to Test:</h4>
				<ol>
					<li>Click "Open Test Modal" to open a modal with test buttons</li>
					<li>Try each test button to verify different confirmation scenarios</li>
					<li>Verify that confirmations appear properly and parent modal is restored</li>
					<li>Check browser console for any errors</li>
				</ol>
			</div>
		</div>
	</div>
</div>

<script>
function openTestModal() {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'xl',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Nested Confirmation System Test',
		iframe: true,
		contenturl: '/?pg=admin&mca_ajaxfunc=showTestModal&mode=stream'
	});
}
</script>
</cfoutput>
